{"_args": [["shebang-regex@1.0.0", "/usr/src/npm/nyc"]], "_from": "shebang-regex@1.0.0", "_id": "shebang-regex@1.0.0", "_inBundle": false, "_integrity": "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=", "_location": "/shebang-regex", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "shebang-regex@1.0.0", "name": "shebang-regex", "escapedName": "shebang-regex", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/shebang-command"], "_resolved": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-1.0.0.tgz", "_spec": "1.0.0", "_where": "/usr/src/npm/nyc", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/shebang-regex/issues"}, "description": "Regular expression for matching a shebang", "devDependencies": {"ava": "0.0.4"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/shebang-regex#readme", "keywords": ["re", "regex", "regexp", "shebang", "match", "test"], "license": "MIT", "name": "shebang-regex", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/shebang-regex.git"}, "scripts": {"test": "node test.js"}, "version": "1.0.0"}