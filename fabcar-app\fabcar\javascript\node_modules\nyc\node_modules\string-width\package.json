{"_args": [["string-width@2.1.1", "/usr/src/npm/nyc"]], "_from": "string-width@2.1.1", "_id": "string-width@2.1.1", "_inBundle": false, "_integrity": "sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw==", "_location": "/string-width", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "string-width@2.1.1", "name": "string-width", "escapedName": "string-width", "rawSpec": "2.1.1", "saveSpec": null, "fetchSpec": "2.1.1"}, "_requiredBy": ["/cliui", "/inquirer", "/standard-version/yargs", "/table", "/yargs"], "_resolved": "https://registry.npmjs.org/string-width/-/string-width-2.1.1.tgz", "_spec": "2.1.1", "_where": "/usr/src/npm/nyc", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dependencies": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}, "description": "Get the visual width of a string - the number of columns required to display it", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/string-width#readme", "keywords": ["string", "str", "character", "char", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "license": "MIT", "name": "string-width", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/string-width.git"}, "scripts": {"test": "xo && ava"}, "version": "2.1.1"}