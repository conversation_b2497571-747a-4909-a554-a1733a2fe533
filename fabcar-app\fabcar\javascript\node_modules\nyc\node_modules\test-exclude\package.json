{"_args": [["test-exclude@5.1.0", "/usr/src/npm/nyc"]], "_from": "test-exclude@5.1.0", "_id": "test-exclude@5.1.0", "_inBundle": false, "_integrity": "sha512-gwf0S2fFsANC55fSeSqpb8BYk6w3FDvwZxfNjeF6FRgvFa43r+7wRiA/Q0IxoRU37wB/LE8IQ4221BsNucTaCA==", "_location": "/test-exclude", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "test-exclude@5.1.0", "name": "test-exclude", "escapedName": "test-exclude", "rawSpec": "5.1.0", "saveSpec": null, "fetchSpec": "5.1.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/test-exclude/-/test-exclude-5.1.0.tgz", "_spec": "5.1.0", "_where": "/usr/src/npm/nyc", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dependencies": {"arrify": "^1.0.1", "minimatch": "^3.0.4", "read-pkg-up": "^4.0.0", "require-main-filename": "^1.0.1"}, "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "engines": {"node": ">=6"}, "files": ["index.js"], "gitHead": "7875defdc3c3640787ac5d83700246de119e8b83", "homepage": "https://github.com/istanbuljs/istanbuljs#readme", "keywords": ["exclude", "include", "glob", "package", "config"], "license": "ISC", "main": "index.js", "name": "test-exclude", "repository": {"type": "git", "url": "git+https://github.com/istanbuljs/istanbuljs.git"}, "scripts": {"test": "mocha"}, "version": "5.1.0"}