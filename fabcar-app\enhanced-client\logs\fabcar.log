{"level":"info","message":"Starting query operations...","service":"fabcar-client","timestamp":"2025-06-24 18:27:17"}
{"level":"info","message":"Starting invoke operations...","service":"fabcar-client","timestamp":"2025-06-24 18:27:34"}
{"level":"info","message":"✅ Enhanced client components loaded successfully","service":"fabcar-client","timestamp":"2025-06-24 18:28:17"}
{"level":"info","message":"Wallet initialized at: C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\wallet","service":"fabcar-client","timestamp":"2025-06-24 18:29:00"}
{"level":"info","message":"FabCar Demo API server running on http://localhost:3000","service":"fabcar-client","timestamp":"2025-06-24 18:30:06"}
{"level":"info","message":"Demo: Retrieved 5 cars","service":"fabcar-client","timestamp":"2025-06-24 18:31:44"}
{"body":"{\\","expose":true,"level":"error","message":"Unhandled error: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"fabcar-client","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:507:28)\n    at endReadableNT (node:internal/streams/readable:1701:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","status":400,"statusCode":400,"timestamp":"2025-06-24 18:32:09","type":"entity.parse.failed"}
{"body":"{\\","expose":true,"level":"error","message":"Unhandled error: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"fabcar-client","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:507:28)\n    at endReadableNT (node:internal/streams/readable:1701:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","status":400,"statusCode":400,"timestamp":"2025-06-24 18:32:32","type":"entity.parse.failed"}
{"level":"info","message":"Demo: Retrieved 5 cars","service":"fabcar-client","timestamp":"2025-06-24 18:33:05"}
{"level":"info","message":"FabCar Demo API server running on http://localhost:3000","service":"fabcar-client","timestamp":"2025-06-26 05:25:37"}
{"level":"info","message":"Wallet initialized at: C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\wallet","service":"fabcar-client","timestamp":"2025-06-26 05:27:30"}
{"level":"info","message":"FabCar Demo API server running on http://localhost:3000","service":"fabcar-client","timestamp":"2025-06-26 05:27:59"}
{"level":"info","message":"Demo: Retrieved 5 cars","service":"fabcar-client","timestamp":"2025-06-26 05:28:40"}
{"level":"info","message":"Demo: Retrieved 5 cars","service":"fabcar-client","timestamp":"2025-06-26 06:06:00"}
{"level":"info","message":"Demo: Retrieved 5 cars","service":"fabcar-client","timestamp":"2025-06-26 07:01:27"}
{"level":"info","message":"Demo: Retrieved 0 cars for owner: Paul","service":"fabcar-client","timestamp":"2025-06-26 07:02:37"}
{"level":"info","message":"Demo: Retrieved 0 cars for make: TATA","service":"fabcar-client","timestamp":"2025-06-26 07:02:39"}
{"level":"info","message":"Demo: Created car: CAR7","service":"fabcar-client","timestamp":"2025-06-26 07:03:40"}
{"level":"info","message":"Demo: Ledger already initialized with sample data","service":"fabcar-client","timestamp":"2025-06-26 07:03:47"}
{"level":"info","message":"Demo: Retrieved 6 cars","service":"fabcar-client","timestamp":"2025-06-26 09:49:12"}
{"level":"info","message":"Demo: Retrieved 6 cars","service":"fabcar-client","timestamp":"2025-06-26 09:50:25"}
{"level":"info","message":"Starting admin enrollment process...","service":"fabcar-client","timestamp":"2025-06-30 18:14:52"}
{"level":"info","message":"Wallet initialized at: C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\wallet","service":"fabcar-client","timestamp":"2025-06-30 18:14:52"}
{"code":"ECONNREFUSED","level":"error","message":"Failed to enroll admin: Calling enroll endpoint failed with error [AggregateError]","service":"fabcar-client","stack":"Error: Calling enroll endpoint failed with error [AggregateError]\n    at ClientRequest.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\node_modules\\fabric-ca-client\\lib\\FabricCAClient.js:327:19)\n    at ClientRequest.emit (node:events:507:28)\n    at emitErrorEvent (node:_http_client:104:11)\n    at TLSSocket.socketErrorListener (node:_http_client:518:5)\n    at TLSSocket.emit (node:events:507:28)\n    at emitErrorNT (node:internal/streams/destroy:170:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:129:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-30 18:14:53"}
{"level":"info","message":"Starting admin enrollment process...","service":"fabcar-client","timestamp":"2025-06-30 18:17:16"}
{"level":"info","message":"Wallet initialized at: C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\wallet","service":"fabcar-client","timestamp":"2025-06-30 18:17:16"}
{"level":"info","message":"Successfully enrolled admin user and imported to wallet","service":"fabcar-client","timestamp":"2025-06-30 18:17:18"}
{"level":"info","message":"Admin enrollment completed successfully","service":"fabcar-client","timestamp":"2025-06-30 18:17:18"}
{"level":"info","message":"Starting user registration process...","service":"fabcar-client","timestamp":"2025-06-30 18:17:31"}
{"level":"info","message":"Wallet initialized at: C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\wallet","service":"fabcar-client","timestamp":"2025-06-30 18:17:32"}
{"level":"error","message":"Failed to register user appUser: gateway.getClient is not a function","service":"fabcar-client","stack":"TypeError: gateway.getClient is not a function\n    at NetworkManager.registerUser (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\utils\\network.js:126:38)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async main (C:\\Users\\<USER>\\Documents\\augment-projects\\FABCAR_BLOCHAIN_APP\\fabcar-app\\enhanced-client\\registerUser.js:22:9)","timestamp":"2025-06-30 18:17:33"}
{"level":"info","message":"Starting query operations...","service":"fabcar-client","timestamp":"2025-06-30 18:17:47"}
