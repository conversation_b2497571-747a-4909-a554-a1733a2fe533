{"_args": [["write-file-atomic@2.4.2", "/usr/src/npm/nyc"]], "_from": "write-file-atomic@2.4.2", "_id": "write-file-atomic@2.4.2", "_inBundle": false, "_integrity": "sha512-s0b6vB3xIVRLWywa6X9TOMA7k9zio0TMOsl9ZnDkliA/cfJlpHXAscj0gbHVJiTdIuAYpIyqS5GW91fqm6gG5g==", "_location": "/write-file-atomic", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "write-file-atomic@2.4.2", "name": "write-file-atomic", "escapedName": "write-file-atomic", "rawSpec": "2.4.2", "saveSpec": null, "fetchSpec": "2.4.2"}, "_requiredBy": ["/caching-transform", "/tap"], "_resolved": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-2.4.2.tgz", "_spec": "2.4.2", "_where": "/usr/src/npm/nyc", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "signal-exit": "^3.0.2"}, "description": "Write files in an atomic fashion w/configurable ownership", "devDependencies": {"mkdirp": "^0.5.1", "require-inject": "^1.4.0", "rimraf": "^2.5.4", "standard": "^12.0.1", "tap": "^12.1.3"}, "files": ["index.js"], "homepage": "https://github.com/iarna/write-file-atomic", "keywords": ["writeFile", "atomic"], "license": "ISC", "main": "index.js", "name": "write-file-atomic", "repository": {"type": "git", "url": "git+ssh://**************/iarna/write-file-atomic.git"}, "scripts": {"test": "standard && tap --100 test/*.js"}, "version": "2.4.2"}