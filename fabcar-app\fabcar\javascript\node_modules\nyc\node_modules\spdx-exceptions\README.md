The package exports an array of strings. Each string is an identifier
for a license exception under the [Software Package Data Exchange
(SPDX)][SPDX] software license metadata standard.

[SPDX]: https://spdx.org

## Copyright and Licensing

### SPDX

"SPDX" is a federally registered United States trademark of The Linux
Foundation Corporation.

From version 2.0 of the [SPDX] specification:

> Copyright © 2010-2015 Linux Foundation and its Contributors. Licensed
> under the Creative Commons Attribution License 3.0 Unported. All other
> rights are expressly reserved.

The Linux Foundation and the SPDX working groups are good people. Only
they decide what "SPDX" means, as a standard and otherwise. I respect
their work and their rights. You should, too.

### This Package

> I created this package by copying exception identifiers out of the
> SPDX specification. That work was mechanical, routine, and required no
> creativity whatsoever. - <PERSON>, package author

United States users concerned about intellectual property may wish to
discuss the following Supreme Court decisions with their attorneys:

- _<PERSON> v. <PERSON>_, 101 U.S. 99 (1879)

- _Feist Publications, Inc., v. Rural Telephone Service Co._,
  499 U.S. 340 (1991)
