{"_args": [["semver@5.6.0", "/usr/src/npm/nyc"]], "_from": "semver@5.6.0", "_id": "semver@5.6.0", "_inBundle": false, "_integrity": "sha512-RS9R6R35NYgQn++fkDWaOmqGoj4Ek9gGs+DPxNUZKuwE183xjJroKvyo1IzVFeXvUrvmALy6FWD5xrdJT25gMg==", "_location": "/semver", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "semver@5.6.0", "name": "semver", "escapedName": "semver", "rawSpec": "5.6.0", "saveSpec": null, "fetchSpec": "5.6.0"}, "_requiredBy": ["/conventional-changelog-writer", "/eslint", "/eslint-plugin-node", "/eslint/cross-spawn", "/execa/cross-spawn", "/git-semver-tags", "/istanbul-lib-instrument", "/normalize-package-data", "/standard-version"], "_resolved": "https://registry.npmjs.org/semver/-/semver-5.6.0.tgz", "_spec": "5.6.0", "_where": "/usr/src/npm/nyc", "bin": {"semver": "./bin/semver"}, "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "description": "The semantic version parser used by npm.", "devDependencies": {"tap": "^12.0.1"}, "files": ["bin", "range.bnf", "semver.js"], "homepage": "https://github.com/npm/node-semver#readme", "license": "ISC", "main": "semver.js", "name": "semver", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "scripts": {"test": "tap test/*.js --cov -J"}, "version": "5.6.0"}