{"_args": [["strip-bom@3.0.0", "/usr/src/npm/nyc"]], "_from": "strip-bom@3.0.0", "_id": "strip-bom@3.0.0", "_inBundle": false, "_integrity": "sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=", "_location": "/strip-bom", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "strip-bom@3.0.0", "name": "strip-bom", "escapedName": "strip-bom", "rawSpec": "3.0.0", "saveSpec": null, "fetchSpec": "3.0.0"}, "_requiredBy": ["/default-require-extensions", "/eslint-plugin-import/load-json-file", "/load-json-file", "/standard-version/load-json-file"], "_resolved": "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz", "_spec": "3.0.0", "_where": "/usr/src/npm/nyc", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/strip-bom/issues"}, "description": "Strip UTF-8 byte order mark (BOM) from a string", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/strip-bom#readme", "keywords": ["strip", "bom", "byte", "order", "mark", "unicode", "utf8", "utf-8", "remove", "delete", "trim", "text", "string"], "license": "MIT", "name": "strip-bom", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/strip-bom.git"}, "scripts": {"test": "xo && ava"}, "version": "3.0.0"}