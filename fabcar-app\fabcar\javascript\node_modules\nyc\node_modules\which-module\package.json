{"_args": [["which-module@2.0.0", "/usr/src/npm/nyc"]], "_from": "which-module@2.0.0", "_id": "which-module@2.0.0", "_inBundle": false, "_integrity": "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=", "_location": "/which-module", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "which-module@2.0.0", "name": "which-module", "escapedName": "which-module", "rawSpec": "2.0.0", "saveSpec": null, "fetchSpec": "2.0.0"}, "_requiredBy": ["/standard-version/yargs", "/yargs"], "_resolved": "https://registry.npmjs.org/which-module/-/which-module-2.0.0.tgz", "_spec": "2.0.0", "_where": "/usr/src/npm/nyc", "author": {"name": "nexdrew"}, "bugs": {"url": "https://github.com/nexdrew/which-module/issues"}, "description": "Find the module object for something that was require()d", "devDependencies": {"ava": "^0.19.1", "coveralls": "^2.13.1", "nyc": "^10.3.0", "standard": "^10.0.2", "standard-version": "^4.0.0"}, "files": ["index.js"], "homepage": "https://github.com/nexdrew/which-module#readme", "keywords": ["which", "module", "exports", "filename", "require", "reverse", "lookup"], "license": "ISC", "main": "index.js", "name": "which-module", "repository": {"type": "git", "url": "git+https://github.com/nexdrew/which-module.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov | coveralls", "pretest": "standard", "release": "standard-version", "test": "nyc ava"}, "version": "2.0.0"}