{"_args": [["spdx-exceptions@2.2.0", "/usr/src/npm/nyc"]], "_from": "spdx-exceptions@2.2.0", "_id": "spdx-exceptions@2.2.0", "_inBundle": false, "_integrity": "sha512-2XQACfElKi9SlVb1CYadKDXvoajPgBVPn/gOQLrTvHdElaVhr7ZEbqJaRnJLVNeaI4cMEAgVCeBMKF6MWRDCRA==", "_location": "/spdx-exceptions", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "spdx-exceptions@2.2.0", "name": "spdx-exceptions", "escapedName": "spdx-exceptions", "rawSpec": "2.2.0", "saveSpec": null, "fetchSpec": "2.2.0"}, "_requiredBy": ["/spdx-expression-parse"], "_resolved": "https://registry.npmjs.org/spdx-exceptions/-/spdx-exceptions-2.2.0.tgz", "_spec": "2.2.0", "_where": "/usr/src/npm/nyc", "author": {"name": "The Linux Foundation"}, "bugs": {"url": "https://github.com/kemitchell/spdx-exceptions.json/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://kemitchell.com/"}], "description": "list of SPDX standard license exceptions", "homepage": "https://github.com/kemitchell/spdx-exceptions.json#readme", "license": "CC-BY-3.0", "name": "spdx-exceptions", "repository": {"type": "git", "url": "git+https://github.com/kemitchell/spdx-exceptions.json.git"}, "version": "2.2.0"}