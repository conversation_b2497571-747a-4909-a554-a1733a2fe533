{"_args": [["rim<PERSON>f@2.6.3", "/usr/src/npm/nyc"]], "_from": "rim<PERSON>f@2.6.3", "_id": "rim<PERSON>f@2.6.3", "_inBundle": false, "_integrity": "sha512-mwqeW5XsA2qAejG46gYdENaxXjx9onRNCfn7L0duuP4hCuTIi/QO7PDK07KJfp1d+izWPrzEJDcSqBa0OZQriA==", "_location": "/rimraf", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "rim<PERSON>f@2.6.3", "name": "<PERSON><PERSON><PERSON>", "escapedName": "<PERSON><PERSON><PERSON>", "rawSpec": "2.6.3", "saveSpec": null, "fetchSpec": "2.6.3"}, "_requiredBy": ["/", "/flat-cache", "/istanbul-lib-source-maps", "/spawn-wrap", "/tap"], "_resolved": "https://registry.npmjs.org/rimraf/-/rimraf-2.6.3.tgz", "_spec": "2.6.3", "_where": "/usr/src/npm/nyc", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bin": {"rimraf": "./bin.js"}, "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "dependencies": {"glob": "^7.1.3"}, "description": "A deep deletion module for node (like `rm -rf`)", "devDependencies": {"mkdirp": "^0.5.1", "tap": "^12.1.1"}, "files": ["LICENSE", "README.md", "bin.js", "rimraf.js"], "homepage": "https://github.com/isaacs/rimraf#readme", "license": "ISC", "main": "rimraf.js", "name": "<PERSON><PERSON><PERSON>", "repository": {"type": "git", "url": "git://github.com/isaacs/rimraf.git"}, "scripts": {"postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish", "preversion": "npm test", "test": "tap test/*.js"}, "version": "2.6.3"}