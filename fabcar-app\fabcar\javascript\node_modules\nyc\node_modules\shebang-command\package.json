{"_args": [["shebang-command@1.2.0", "/usr/src/npm/nyc"]], "_from": "shebang-command@1.2.0", "_id": "shebang-command@1.2.0", "_inBundle": false, "_integrity": "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=", "_location": "/shebang-command", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "shebang-command@1.2.0", "name": "shebang-command", "escapedName": "shebang-command", "rawSpec": "1.2.0", "saveSpec": null, "fetchSpec": "1.2.0"}, "_requiredBy": ["/eslint/cross-spawn", "/execa/cross-spawn", "/standard-version/cross-spawn"], "_resolved": "https://registry.npmjs.org/shebang-command/-/shebang-command-1.2.0.tgz", "_spec": "1.2.0", "_where": "/usr/src/npm/nyc", "author": {"name": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "github.com/kevva"}, "bugs": {"url": "https://github.com/kevva/shebang-command/issues"}, "dependencies": {"shebang-regex": "^1.0.0"}, "description": "Get the command from a shebang", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/kevva/shebang-command#readme", "keywords": ["cmd", "command", "parse", "shebang"], "license": "MIT", "name": "shebang-command", "repository": {"type": "git", "url": "git+https://github.com/kevva/shebang-command.git"}, "scripts": {"test": "xo && ava"}, "version": "1.2.0", "xo": {"ignores": ["test.js"]}}