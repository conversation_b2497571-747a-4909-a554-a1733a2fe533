{"_args": [["resolve@1.10.0", "/usr/src/npm/nyc"]], "_from": "resolve@1.10.0", "_id": "resolve@1.10.0", "_inBundle": false, "_integrity": "sha512-3sUr9aq5OfSg2S9pNtPA9hL1FVEAjvfOC4leW0SNf/mpnaakz2a9femSd6LqAww2RaFctwyf1lCqnTHuF1rxDg==", "_location": "/resolve", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "resolve@1.10.0", "name": "resolve", "escapedName": "resolve", "rawSpec": "1.10.0", "saveSpec": null, "fetchSpec": "1.10.0"}, "_requiredBy": ["/eslint-import-resolver-node", "/eslint-plugin-import", "/eslint-plugin-node", "/normalize-package-data"], "_resolved": "https://registry.npmjs.org/resolve/-/resolve-1.10.0.tgz", "_spec": "1.10.0", "_where": "/usr/src/npm/nyc", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dependencies": {"path-parse": "^1.0.6"}, "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "devDependencies": {"@ljharb/eslint-config": "^13.1.1", "eslint": "^5.12.0", "object-keys": "^1.0.12", "safe-publish-latest": "^1.1.2", "tap": "0.4.13", "tape": "^4.9.2"}, "homepage": "https://github.com/browserify/resolve#readme", "keywords": ["resolve", "require", "node", "module"], "license": "MIT", "main": "index.js", "name": "resolve", "repository": {"type": "git", "url": "git://github.com/browserify/resolve.git"}, "scripts": {"lint": "eslint .", "posttest": "npm run test:multirepo", "prepublish": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run --silent tests-only", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test", "tests-only": "tape test/*.js"}, "version": "1.10.0"}