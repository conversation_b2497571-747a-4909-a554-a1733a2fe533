{"_args": [["validate-npm-package-license@3.0.4", "/usr/src/npm/nyc"]], "_from": "validate-npm-package-license@3.0.4", "_id": "validate-npm-package-license@3.0.4", "_inBundle": false, "_integrity": "sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==", "_location": "/validate-npm-package-license", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "validate-npm-package-license@3.0.4", "name": "validate-npm-package-license", "escapedName": "validate-npm-package-license", "rawSpec": "3.0.4", "saveSpec": null, "fetchSpec": "3.0.4"}, "_requiredBy": ["/normalize-package-data"], "_resolved": "https://registry.npmjs.org/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz", "_spec": "3.0.4", "_where": "/usr/src/npm/nyc", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://kemitchell.com"}, "bugs": {"url": "https://github.com/kemitchell/validate-npm-package-license.js/issues"}, "contributors": [{"name": "<PERSON>", "email": "markj<PERSON><PERSON>@gmail.com"}], "dependencies": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}, "description": "Give me a string and I'll tell you if it's a valid npm package license string", "devDependencies": {"defence-cli": "^2.0.1", "replace-require-self": "^1.0.0"}, "homepage": "https://github.com/kemitchell/validate-npm-package-license.js#readme", "keywords": ["license", "npm", "package", "validation"], "license": "Apache-2.0", "name": "validate-npm-package-license", "repository": {"type": "git", "url": "git+https://github.com/kemitchell/validate-npm-package-license.js.git"}, "scripts": {"test": "defence README.md | replace-require-self | node"}, "version": "3.0.4"}