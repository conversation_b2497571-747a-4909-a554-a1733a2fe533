{"_args": [["wrap-ansi@2.1.0", "/usr/src/npm/nyc"]], "_from": "wrap-ansi@2.1.0", "_id": "wrap-ansi@2.1.0", "_inBundle": false, "_integrity": "sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU=", "_location": "/wrap-ansi", "_phantomChildren": {"code-point-at": "1.1.0", "number-is-nan": "1.0.1"}, "_requested": {"type": "version", "registry": true, "raw": "wrap-ansi@2.1.0", "name": "wrap-ansi", "escapedName": "wrap-ansi", "rawSpec": "2.1.0", "saveSpec": null, "fetchSpec": "2.1.0"}, "_requiredBy": ["/cliui", "/standard-version/cliui"], "_resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-2.1.0.tgz", "_spec": "2.1.0", "_where": "/usr/src/npm/nyc", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "dependencies": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1"}, "description": "Wordwrap a string with ANSI escape codes", "devDependencies": {"ava": "^0.16.0", "chalk": "^1.1.0", "coveralls": "^2.11.4", "has-ansi": "^2.0.0", "nyc": "^6.2.1", "strip-ansi": "^3.0.0", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/chalk/wrap-ansi#readme", "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "jbnicolai.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/qix-"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/bcoe"}], "name": "wrap-ansi", "repository": {"type": "git", "url": "git+https://github.com/chalk/wrap-ansi.git"}, "scripts": {"coveralls": "nyc report --reporter=text-lcov | coveralls", "test": "xo && nyc ava"}, "version": "2.1.0"}