{"_args": [["strip-eof@1.0.0", "/usr/src/npm/nyc"]], "_from": "strip-eof@1.0.0", "_id": "strip-eof@1.0.0", "_inBundle": false, "_integrity": "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=", "_location": "/strip-eof", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "strip-eof@1.0.0", "name": "strip-eof", "escapedName": "strip-eof", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/execa", "/standard-version/execa"], "_resolved": "https://registry.npmjs.org/strip-eof/-/strip-eof-1.0.0.tgz", "_spec": "1.0.0", "_where": "/usr/src/npm/nyc", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/strip-eof/issues"}, "description": "Strip the End-Of-File (EOF) character from a string/buffer", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/strip-eof#readme", "keywords": ["strip", "trim", "remove", "delete", "eof", "end", "file", "newline", "linebreak", "character", "string", "buffer"], "license": "MIT", "name": "strip-eof", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/strip-eof.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.0"}