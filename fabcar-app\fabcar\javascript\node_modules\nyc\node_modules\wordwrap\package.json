{"_args": [["wordwrap@0.0.3", "/usr/src/npm/nyc"]], "_from": "wordwrap@0.0.3", "_id": "wordwrap@0.0.3", "_inBundle": false, "_integrity": "sha1-o9XabNXAvAAI03I0u68b7WMFkQc=", "_location": "/wordwrap", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "wordwrap@0.0.3", "name": "wordwrap", "escapedName": "wordwrap", "rawSpec": "0.0.3", "saveSpec": null, "fetchSpec": "0.0.3"}, "_requiredBy": ["/optimist"], "_resolved": "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.3.tgz", "_spec": "0.0.3", "_where": "/usr/src/npm/nyc", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/substack/node-wordwrap/issues"}, "description": "Wrap those words. Show them at what columns to start and stop.", "devDependencies": {"expresso": "=0.7.x"}, "directories": {"lib": ".", "example": "example", "test": "test"}, "engines": {"node": ">=0.4.0"}, "homepage": "https://github.com/substack/node-wordwrap#readme", "keywords": ["word", "wrap", "rule", "format", "column"], "license": "MIT", "main": "./index.js", "name": "wordwrap", "repository": {"type": "git", "url": "git://github.com/substack/node-wordwrap.git"}, "scripts": {"test": "expresso"}, "version": "0.0.3"}