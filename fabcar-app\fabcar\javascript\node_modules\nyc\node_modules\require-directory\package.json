{"_args": [["require-directory@2.1.1", "/usr/src/npm/nyc"]], "_from": "require-directory@2.1.1", "_id": "require-directory@2.1.1", "_inBundle": false, "_integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I=", "_location": "/require-directory", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "require-directory@2.1.1", "name": "require-directory", "escapedName": "require-directory", "rawSpec": "2.1.1", "saveSpec": null, "fetchSpec": "2.1.1"}, "_requiredBy": ["/standard-version/yargs", "/yargs"], "_resolved": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "_spec": "2.1.1", "_where": "/usr/src/npm/nyc", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}, "bugs": {"url": "http://github.com/troygoode/node-require-directory/issues/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}], "description": "Recursively iterates over specified directory, require()'ing each file, and returning a nested hash structure containing those modules.", "devDependencies": {"jshint": "^2.6.0", "mocha": "^2.1.0"}, "engines": {"node": ">=0.10.0"}, "homepage": "https://github.com/troygoode/node-require-directory/", "keywords": ["require", "directory", "library", "recursive"], "license": "MIT", "main": "index.js", "name": "require-directory", "repository": {"type": "git", "url": "git://github.com/troygoode/node-require-directory.git"}, "scripts": {"lint": "jshint index.js test/test.js", "test": "mocha"}, "version": "2.1.1"}