{"_args": [["spdx-expression-parse@3.0.0", "/usr/src/npm/nyc"]], "_from": "spdx-expression-parse@3.0.0", "_id": "spdx-expression-parse@3.0.0", "_inBundle": false, "_integrity": "sha512-Yg6D3XpRD4kkOmTpdgbUiEJFKghJH03fiC1OPll5h/0sO6neh2jqRDVHOQ4o/LMea0tgCkbMgea5ip/e+MkWyg==", "_location": "/spdx-expression-parse", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "spdx-expression-parse@3.0.0", "name": "spdx-expression-parse", "escapedName": "spdx-expression-parse", "rawSpec": "3.0.0", "saveSpec": null, "fetchSpec": "3.0.0"}, "_requiredBy": ["/spdx-correct", "/validate-npm-package-license"], "_resolved": "https://registry.npmjs.org/spdx-expression-parse/-/spdx-expression-parse-3.0.0.tgz", "_spec": "3.0.0", "_where": "/usr/src/npm/nyc", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kemitchell.com"}, "bugs": {"url": "https://github.com/jslicense/spdx-expression-parse.js/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://cscott.net"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://kemitchell.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}, "description": "parse SPDX license expressions", "devDependencies": {"defence-cli": "^2.0.1", "mocha": "^3.4.2", "replace-require-self": "^1.0.0", "standard": "^10.0.2"}, "files": ["AUTHORS", "index.js", "parse.js", "scan.js"], "homepage": "https://github.com/jslicense/spdx-expression-parse.js#readme", "keywords": ["SPDX", "law", "legal", "license", "metadata", "package", "package.json", "standards"], "license": "MIT", "name": "spdx-expression-parse", "repository": {"type": "git", "url": "git+https://github.com/jslicense/spdx-expression-parse.js.git"}, "scripts": {"lint": "standard", "test": "npm run test:mocha && npm run test:readme", "test:mocha": "mocha test/index.js", "test:readme": "defence -i javascript README.md | replace-require-self | node"}, "version": "3.0.0"}