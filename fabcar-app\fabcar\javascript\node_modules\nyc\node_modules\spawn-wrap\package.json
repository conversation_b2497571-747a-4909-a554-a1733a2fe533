{"_args": [["spawn-wrap@1.4.2", "/usr/src/npm/nyc"]], "_from": "spawn-wrap@1.4.2", "_id": "spawn-wrap@1.4.2", "_inBundle": false, "_integrity": "sha512-vMwR3OmmDhnxCVxM8M+xO/FtIp6Ju/mNaDfCMMW7FDcLRTPFWUswec4LXJHTJE2hwTI9O0YBfygu4DalFl7Ylg==", "_location": "/spawn-wrap", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "spawn-wrap@1.4.2", "name": "spawn-wrap", "escapedName": "spawn-wrap", "rawSpec": "1.4.2", "saveSpec": null, "fetchSpec": "1.4.2"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/spawn-wrap/-/spawn-wrap-1.4.2.tgz", "_spec": "1.4.2", "_where": "/usr/src/npm/nyc", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/spawn-wrap/issues"}, "dependencies": {"foreground-child": "^1.5.6", "mkdirp": "^0.5.0", "os-homedir": "^1.0.1", "rimraf": "^2.6.2", "signal-exit": "^3.0.2", "which": "^1.3.0"}, "description": "Wrap all spawned Node.js child processes by adding environs and arguments ahead of the main JavaScript file argument.", "devDependencies": {"tap": "^10.7.3"}, "files": ["index.js", "shim.js", "lib/is-windows.js"], "homepage": "https://github.com/isaacs/spawn-wrap#readme", "license": "ISC", "main": "index.js", "name": "spawn-wrap", "repository": {"type": "git", "url": "git+https://github.com/isaacs/spawn-wrap.git"}, "scripts": {"clean": "rm -rf ~/.node-spawn-wrap-*", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish", "preversion": "npm test", "test": "tap --timeout=240 test/*.js"}, "version": "1.4.2"}