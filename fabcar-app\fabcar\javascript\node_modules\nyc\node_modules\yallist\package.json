{"_args": [["yallist@2.1.2", "/usr/src/npm/nyc"]], "_from": "yallist@2.1.2", "_id": "yallist@2.1.2", "_inBundle": false, "_integrity": "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=", "_location": "/yallist", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "yallist@2.1.2", "name": "yallist", "escapedName": "yallist", "rawSpec": "2.1.2", "saveSpec": null, "fetchSpec": "2.1.2"}, "_requiredBy": ["/lru-cache"], "_resolved": "https://registry.npmjs.org/yallist/-/yallist-2.1.2.tgz", "_spec": "2.1.2", "_where": "/usr/src/npm/nyc", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "dependencies": {}, "description": "Yet Another Linked List", "devDependencies": {"tap": "^10.3.0"}, "directories": {"test": "test"}, "files": ["yallist.js", "iterator.js"], "homepage": "https://github.com/isaacs/yallist#readme", "license": "ISC", "main": "yallist.js", "name": "yallist", "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "scripts": {"postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish", "preversion": "npm test", "test": "tap test/*.js --100"}, "version": "2.1.2"}