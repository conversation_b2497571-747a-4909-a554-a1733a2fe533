{"_args": [["yargs@12.0.5", "/usr/src/npm/nyc"]], "_from": "yargs@12.0.5", "_id": "yargs@12.0.5", "_inBundle": false, "_integrity": "sha512-Lhz8TLaYnxq/2ObqHDql8dX8CJi97oHxrjUcYtzKbbykPtVW9WB+poxI+NM2UIzsMgNCZTIf0AQwsjK5yMAqZw==", "_location": "/yargs", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "yargs@12.0.5", "name": "yargs", "escapedName": "yargs", "rawSpec": "12.0.5", "saveSpec": null, "fetchSpec": "12.0.5"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/yargs/-/yargs-12.0.5.tgz", "_spec": "12.0.5", "_where": "/usr/src/npm/nyc", "bugs": {"url": "https://github.com/yargs/yargs/issues"}, "contributors": [{"name": "Yargs Contributors", "url": "https://github.com/yargs/yargs/graphs/contributors"}], "dependencies": {"cliui": "^4.0.0", "decamelize": "^1.2.0", "find-up": "^3.0.0", "get-caller-file": "^1.0.1", "os-locale": "^3.0.0", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^2.0.0", "which-module": "^2.0.0", "y18n": "^3.2.1 || ^4.0.0", "yargs-parser": "^11.1.1"}, "description": "yargs the modern, pirate-themed, successor to optimist.", "devDependencies": {"chai": "^4.1.2", "chalk": "^1.1.3", "coveralls": "^3.0.2", "cpr": "^2.0.0", "cross-spawn": "^6.0.4", "es6-promise": "^4.0.2", "hashish": "0.0.4", "mocha": "^5.1.1", "nyc": "^11.7.3", "rimraf": "^2.5.0", "standard": "^11.0.1", "standard-version": "^4.2.0", "which": "^1.2.9", "yargs-test-extends": "^1.0.1"}, "engine": {"node": ">=6"}, "files": ["index.js", "yargs.js", "lib", "locales", "completion.sh.hbs", "LICENSE"], "homepage": "https://yargs.js.org/", "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "license": "MIT", "main": "./index.js", "name": "yargs", "repository": {"type": "git", "url": "git+https://github.com/yargs/yargs.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov | coveralls", "pretest": "standard", "release": "standard-version", "test": "nyc --cache mocha --require ./test/before.js --timeout=8000 --check-leaks"}, "standard": {"ignore": ["**/example/**"]}, "version": "12.0.5"}