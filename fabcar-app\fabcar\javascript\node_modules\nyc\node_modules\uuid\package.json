{"_args": [["uuid@3.3.2", "/usr/src/npm/nyc"]], "_from": "uuid@3.3.2", "_id": "uuid@3.3.2", "_inBundle": false, "_integrity": "sha512-yXJmeNaw3DnnKAOKJE51sL/ZaYfWJRl1pK9dr19YFCu0ObS231AB1/LbqTKRAQ5kw8A90rA6fr4riOUpTZvQZA==", "_location": "/uuid", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "uuid@3.3.2", "name": "uuid", "escapedName": "uuid", "rawSpec": "3.3.2", "saveSpec": null, "fetchSpec": "3.3.2"}, "_requiredBy": ["/", "/request"], "_resolved": "https://registry.npmjs.org/uuid/-/uuid-3.3.2.tgz", "_spec": "3.3.2", "_where": "/usr/src/npm/nyc", "bin": {"uuid": "./bin/uuid"}, "browser": {"./lib/rng.js": "./lib/rng-browser.js", "./lib/sha1.js": "./lib/sha1-browser.js", "./lib/md5.js": "./lib/md5-browser.js"}, "bugs": {"url": "https://github.com/kelektiv/node-uuid/issues"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "RFC4122 (v1, v4, and v5) UUIDs", "devDependencies": {"@commitlint/cli": "7.0.0", "@commitlint/config-conventional": "7.0.1", "eslint": "4.19.1", "husky": "0.14.3", "mocha": "5.2.0", "runmd": "1.0.1", "standard-version": "4.4.0"}, "homepage": "https://github.com/kelektiv/node-uuid#readme", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "name": "uuid", "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node-uuid.git"}, "scripts": {"commitmsg": "commitlint -E GIT_PARAMS", "md": "runmd --watch --output=README.md README_js.md", "prepare": "runmd --output=README.md README_js.md", "release": "standard-version", "test": "mocha test/test.js"}, "version": "3.3.2"}