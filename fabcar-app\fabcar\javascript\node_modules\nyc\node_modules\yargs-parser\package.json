{"_args": [["yargs-parser@11.1.1", "/usr/src/npm/nyc"]], "_from": "yargs-parser@11.1.1", "_id": "yargs-parser@11.1.1", "_inBundle": false, "_integrity": "sha512-C6kB/WJDiaxONLJQnF8ccx9SEeoTTLek8RVbaOIsrAUS8VrBEXfmeSnCZxygc+XC2sNMBIwOOnfcxiynjHsVSQ==", "_location": "/yargs-parser", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "yargs-parser@11.1.1", "name": "yargs-parser", "escapedName": "yargs-parser", "rawSpec": "11.1.1", "saveSpec": null, "fetchSpec": "11.1.1"}, "_requiredBy": ["/", "/yargs"], "_resolved": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-11.1.1.tgz", "_spec": "11.1.1", "_where": "/usr/src/npm/nyc", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "description": "the mighty option parser used by yargs", "devDependencies": {"chai": "^4.2.0", "coveralls": "^3.0.2", "mocha": "^5.2.0", "nyc": "^13.0.1", "standard": "^12.0.1", "standard-version": "^4.4.0"}, "engine": {"node": ">=6"}, "files": ["lib", "index.js"], "homepage": "https://github.com/yargs/yargs-parser#readme", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "license": "ISC", "main": "index.js", "name": "yargs-parser", "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov | coveralls", "posttest": "standard", "release": "standard-version", "test": "nyc mocha test/*.js"}, "version": "11.1.1"}