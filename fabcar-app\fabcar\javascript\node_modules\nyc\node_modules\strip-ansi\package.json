{"_args": [["strip-ansi@4.0.0", "/usr/src/npm/nyc"]], "_from": "strip-ansi@4.0.0", "_id": "strip-ansi@4.0.0", "_inBundle": false, "_integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "_location": "/strip-ansi", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "strip-ansi@4.0.0", "name": "strip-ansi", "escapedName": "strip-ansi", "rawSpec": "4.0.0", "saveSpec": null, "fetchSpec": "4.0.0"}, "_requiredBy": ["/cliui", "/eslint", "/inquirer", "/string-width"], "_resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-4.0.0.tgz", "_spec": "4.0.0", "_where": "/usr/src/npm/nyc", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/chalk/strip-ansi/issues"}, "dependencies": {"ansi-regex": "^3.0.0"}, "description": "Strip ANSI escape codes", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/chalk/strip-ansi#readme", "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "name": "strip-ansi", "repository": {"type": "git", "url": "git+https://github.com/chalk/strip-ansi.git"}, "scripts": {"test": "xo && ava"}, "version": "4.0.0"}