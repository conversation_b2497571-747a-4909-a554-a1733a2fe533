{"_args": [["spdx-correct@3.1.0", "/usr/src/npm/nyc"]], "_from": "spdx-correct@3.1.0", "_id": "spdx-correct@3.1.0", "_inBundle": false, "_integrity": "sha512-lr2EZCctC2BNR7j7WzJ2FpDznxky1sjfxvvYEyzxNyb6lZXHODmEoJeFu4JupYlkfha1KZpJyoqiJ7pgA1qq8Q==", "_location": "/spdx-correct", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "spdx-correct@3.1.0", "name": "spdx-correct", "escapedName": "spdx-correct", "rawSpec": "3.1.0", "saveSpec": null, "fetchSpec": "3.1.0"}, "_requiredBy": ["/validate-npm-package-license"], "_resolved": "https://registry.npmjs.org/spdx-correct/-/spdx-correct-3.1.0.tgz", "_spec": "3.1.0", "_where": "/usr/src/npm/nyc", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://kemitchell.com"}, "bugs": {"url": "https://github.com/jslicense/spdx-correct.js/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://kemitchell.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}, "description": "correct invalid SPDX expressions", "devDependencies": {"defence-cli": "^2.0.1", "replace-require-self": "^1.0.0", "standard": "^11.0.0", "standard-markdown": "^4.0.2", "tape": "^4.9.0"}, "files": ["index.js"], "homepage": "https://github.com/jslicense/spdx-correct.js#readme", "keywords": ["SPDX", "law", "legal", "license", "metadata"], "license": "Apache-2.0", "name": "spdx-correct", "repository": {"type": "git", "url": "git+https://github.com/jslicense/spdx-correct.js.git"}, "scripts": {"lint": "standard && standard-markdown README.md", "test": "defence README.md | replace-require-self | node && node test.js"}, "version": "3.1.0"}