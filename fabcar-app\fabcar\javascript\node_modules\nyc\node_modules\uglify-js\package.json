{"_args": [["uglify-js@3.4.9", "/usr/src/npm/nyc"]], "_from": "uglify-js@3.4.9", "_id": "uglify-js@3.4.9", "_inBundle": false, "_integrity": "sha512-8<PERSON><PERSON>sbKOtEbnJsTyv6LE6m6ZKniqMiFWmm9sRbopbkGs3gMPPfd3Fh8iIA4Ykv5MgaTbqHr4BaoGLJLZNhsrW1Q==", "_location": "/uglify-js", "_optional": true, "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "uglify-js@3.4.9", "name": "uglify-js", "escapedName": "uglify-js", "rawSpec": "3.4.9", "saveSpec": null, "fetchSpec": "3.4.9"}, "_requiredBy": ["/handlebars"], "_resolved": "https://registry.npmjs.org/uglify-js/-/uglify-js-3.4.9.tgz", "_spec": "3.4.9", "_where": "/usr/src/npm/nyc", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://lisperator.net/"}, "bin": {"uglifyjs": "bin/uglifyjs"}, "bugs": {"url": "https://github.com/mishoo/UglifyJS2/issues"}, "dependencies": {"commander": "~2.17.1", "source-map": "~0.6.1"}, "description": "JavaScript parser, mangler/compressor and beautifier toolkit", "devDependencies": {"acorn": "~5.7.1", "semver": "~5.5.0"}, "engines": {"node": ">=0.8.0"}, "files": ["bin", "lib", "tools", "LICENSE"], "homepage": "https://github.com/mishoo/UglifyJS2#readme", "keywords": ["cli", "compress", "compressor", "ecma", "ecmascript", "es", "es5", "javascript", "js", "jsmin", "min", "minification", "minifier", "minify", "optimize", "optimizer", "pack", "packer", "parse", "parser", "uglifier", "uglify"], "license": "BSD-2-<PERSON><PERSON>", "main": "tools/node.js", "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://lisperator.net/"}], "name": "uglify-js", "repository": {"type": "git", "url": "git+https://github.com/mishoo/UglifyJS2.git"}, "scripts": {"test": "node test/run-tests.js"}, "version": "3.4.9"}